import React from 'react';
import { Play, Pause, SkipBack as Skip, Volume2, VolumeX, <PERSON>wind, <PERSON>For<PERSON>, StopCircle, Settings } from 'lucide-react';
import { 
  Box, 
  IconButton, 
  Slider, 
  Typography, 
  Paper,
  Menu,
  MenuItem,
  Select,
  FormControl,
  InputLabel,
} from '@mui/material';
import type { VoiceOptions } from '../types';
import type { SelectChangeEvent } from '@mui/material/Select';

interface AudioControlsProps {
  playing: boolean;
  paused: boolean;
  onPlay: () => void;
  onPause: () => void;
  onResume: () => void;
  onStop: () => void;
  onNext: () => void;
  onPrevious: () => void;
  voiceOptions: VoiceOptions;
  setVoiceOptions: (options: Partial<VoiceOptions>) => void;
  availableVoices: SpeechSynthesisVoice[];
}

const AudioControls: React.FC<AudioControlsProps> = ({
  playing,
  paused,
  onPlay,
  onPause,
  onResume,
  onStop,
  onNext,
  onPrevious,
  voiceOptions,
  setVoiceOptions,
  availableVoices
}) => {
  const [settingsAnchorEl, setSettingsAnchorEl] = React.useState<null | HTMLElement>(null);
  const settingsOpen = Boolean(settingsAnchorEl);
  
  const handleSettingsClick = (event: React.MouseEvent<HTMLButtonElement>) => {
    setSettingsAnchorEl(event.currentTarget);
  };
  
  const handleSettingsClose = () => {
    setSettingsAnchorEl(null);
  };
  
  const handleVoiceChange = (event: SelectChangeEvent<string>) => {
    const voiceURI = event.target.value;
    const selectedVoice = availableVoices.find(voice => voice.voiceURI === voiceURI) || null;
    setVoiceOptions({ voice: selectedVoice });
  };
  
  return (
    <Box className="flex items-center gap-4">
      {/* Main playback controls */}
      <Box className="flex items-center">
        <IconButton
          onClick={onPrevious}
          aria-label="Previous"
          className="text-blue-600"
          size="small"
        >
          <Rewind size={20} />
        </IconButton>

        {playing && !paused ? (
          <IconButton
            onClick={onPause}
            aria-label="Pause"
            className="text-blue-600 mx-1"
            size="medium"
          >
            <Pause size={28} />
          </IconButton>
        ) : playing && paused ? (
          <IconButton
            onClick={onResume}
            aria-label="Resume"
            className="text-blue-600 mx-1"
            size="medium"
          >
            <Play size={28} />
          </IconButton>
        ) : (
          <IconButton
            onClick={onPlay}
            aria-label="Play"
            className="text-blue-600 mx-1"
            size="medium"
          >
            <Play size={28} />
          </IconButton>
        )}

        <IconButton
          onClick={onNext}
          aria-label="Next"
          className="text-blue-600"
          size="small"
        >
          <FastForward size={20} />
        </IconButton>

        <IconButton
          onClick={onStop}
          aria-label="Stop"
          className="text-red-500 ml-2"
          disabled={!playing}
          size="small"
        >
          <StopCircle size={20} />
        </IconButton>
      </Box>

      {/* Volume control */}
      <Box className="flex items-center" sx={{ minWidth: 120 }}>
        {voiceOptions.volume === 0 ? (
          <VolumeX size={18} className="text-gray-500 mr-2" />
        ) : (
          <Volume2 size={18} className="text-gray-500 mr-2" />
        )}
        <Slider
          value={voiceOptions.volume}
          min={0}
          max={1}
          step={0.1}
          onChange={(_, value) => setVoiceOptions({ volume: value as number })}
          aria-label="Volume"
          size="small"
          sx={{ width: 80 }}
        />
      </Box>

      {/* Settings button */}
      <IconButton
        size="small"
        onClick={handleSettingsClick}
        aria-label="Voice settings"
        className="text-gray-600"
      >
        <Settings size={20} />
      </IconButton>

      {/* Settings menu */}
      <Menu
        anchorEl={settingsAnchorEl}
        open={settingsOpen}
        onClose={handleSettingsClose}
        PaperProps={{
          style: {
            width: 250,
            padding: '8px'
          },
        }}
      >
        <Typography variant="subtitle2" className="px-3 py-1">
          Voice Settings
        </Typography>

        <FormControl fullWidth margin="dense" size="small">
          <InputLabel id="voice-select-label">Voice</InputLabel>
          <Select
            labelId="voice-select-label"
            value={voiceOptions.voice?.voiceURI || ''}
            onChange={handleVoiceChange}
            label="Voice"
          >
            {availableVoices.map((voice) => (
              <MenuItem key={voice.voiceURI} value={voice.voiceURI}>
                {voice.name} ({voice.lang})
              </MenuItem>
            ))}
          </Select>
        </FormControl>

        <Box className="px-3 py-2">
          <Typography variant="body2" gutterBottom>
            Speed: {voiceOptions.rate.toFixed(1)}x
          </Typography>
          <Slider
            value={voiceOptions.rate}
            min={0.5}
            max={2}
            step={0.1}
            onChange={(_, value) => setVoiceOptions({ rate: value as number })}
            aria-label="Speech rate"
            size="small"
          />
        </Box>

        <Box className="px-3 py-2">
          <Typography variant="body2" gutterBottom>
            Pitch: {voiceOptions.pitch.toFixed(1)}
          </Typography>
          <Slider
            value={voiceOptions.pitch}
            min={0.5}
            max={2}
            step={0.1}
            onChange={(_, value) => setVoiceOptions({ pitch: value as number })}
            aria-label="Speech pitch"
            size="small"
          />
        </Box>
      </Menu>
    </Box>
  );
};

export default AudioControls;