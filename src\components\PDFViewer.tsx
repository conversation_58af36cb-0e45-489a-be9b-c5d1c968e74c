import React, { useEffect, useRef, useState } from 'react';
import { Document, Page, pdfjs } from 'react-pdf';
import HTMLFlipBook from 'react-pageflip';
import 'react-pdf/dist/esm/Page/AnnotationLayer.css';
import 'react-pdf/dist/esm/Page/TextLayer.css';
import { ChevronLeft, ChevronRight, Play, Pause, StopCircle } from 'lucide-react';
import {
  Box,
  IconButton,
  Paper,
  Typography,
  useMediaQuery,
  useTheme,
  Tooltip,
  Divider
} from '@mui/material';
import type { PDFDocument, HighlightArea, TextItem } from '../types';

pdfjs.GlobalWorkerOptions.workerSrc = `https://cdn.jsdelivr.net/npm/pdfjs-dist@${pdfjs.version}/build/pdf.worker.min.js`;

// interface PDFViewerProps {
//   document: PDFDocument | null;
//   highlights: HighlightArea[];
//   onPageChange: (pageNumber: number) => Promise<void>;
//   onTextSelection: (text: string, pageIndex: number) => void;
//   onReadingComplete?: () => void;
//   currentWord?: string; // For real-time word highlighting
//   onStartReading?: () => void; // Callback when reading starts
//   onStopReading?: () => void; // Callback when reading stops
//   isReading?: boolean; // Whether the PDF is currently being read
// }

// const PDFViewer: React.FC<PDFViewerProps> = ({
//   document,
//   highlights,
//   onPageChange,
//   onTextSelection,
//   onReadingComplete,
//   currentWord,
//   onStartReading,
//   onStopReading,
//   isReading = false,
// }) => {
//   const theme = useTheme();
//   const isMobile = useMediaQuery(theme.breakpoints.down('sm'));
//   const [numPages, setNumPages] = useState<number | null>(null);
//   const [pageNumber, setPageNumber] = useState<number>(1);
//   const [scale, setScale] = useState<number>(isMobile ? 0.8 : 1.2);
//   const [isLoading, setIsLoading] = useState<boolean>(true);
//   const [error, setError] = useState<string | null>(null);
//   const [renderedPages, setRenderedPages] = useState<Set<number>>(new Set([1]));
//   const flipBookRef = useRef<any>(null);
//   const containerRef = useRef<HTMLDivElement>(null);

//   useEffect(() => {
//     if (document) {
//       setPageNumber(document.currentPage);
//       setIsLoading(true);
//       setError(null);
//     }
//   }, [document]);

//   useEffect(() => {
//     if (document && flipBookRef.current) {
//       const pageFlipInstance = flipBookRef.current.pageFlip();
//       const targetPage = document.currentPage - 1;
//       const currentPage = pageFlipInstance.getCurrentPageIndex();
//       if (currentPage !== targetPage) {
//         const delta = targetPage - currentPage;
//         if (delta > 0) {
//           for (let i = 0; i < delta; i++) {
//             pageFlipInstance.flipNext();
//           }
//         } else if (delta < 0) {
//           for (let i = 0; i < -delta; i++) {
//             pageFlipInstance.flipPrev();
//           }
//         }
//       }
//     }
//   }, [document?.currentPage]);

//   useEffect(() => {
//     const handleResize = () => {
//       if (containerRef.current) {
//         const width = containerRef.current.offsetWidth;
//         setScale(isMobile ? width / 800 : width / 1200);
//       }
//     };
//     handleResize();
//     window.addEventListener('resize', handleResize);
//     return () => window.removeEventListener('resize', handleResize);
//   }, [isMobile]);

//   useEffect(() => {
//     if (numPages && pageNumber) {
//       const pagesToRender = new Set<number>();
//       pagesToRender.add(pageNumber);
//       if (pageNumber > 1) pagesToRender.add(pageNumber - 1);
//       if (pageNumber < numPages) pagesToRender.add(pageNumber + 1);
//       setRenderedPages(pagesToRender);
//     }
//   }, [pageNumber, numPages]);

//   const onDocumentLoadSuccess = ({ numPages }: { numPages: number }) => {
//     setNumPages(numPages);
//     setIsLoading(false);
//   };

//   const onDocumentLoadError = (error: Error) => {
//     console.error('Error loading PDF:', error);
//     setError('Failed to load the PDF document.');
//     setIsLoading(false);
//   };

//   const handlePageFlip = (e: any) => {
//     const newPageNumber = e.data + 1;
//     setPageNumber(newPageNumber);
//     onPageChange(newPageNumber);
//   };

//   const goToPrevPage = () => {
//     if (flipBookRef.current && pageNumber > 1) {
//       flipBookRef.current.pageFlip().flipPrev();
//     }
//   };

//   const goToNextPage = () => {
//     if (flipBookRef.current && numPages !== null && pageNumber < numPages) {
//       flipBookRef.current.pageFlip().flipNext();
//     }
//   };

//   const handleTextSelection = () => {
//     if (isReading) return; // Disable selection during reading
//     const selection = window.getSelection();
//     if (selection && selection.toString()) {
//       onTextSelection(selection.toString(), pageNumber - 1);
//     }
//   };

//   const handleStartReading = () => {
//     if (onStartReading) {
//       onStartReading();
//     }
//   };

//   const handleStopReading = () => {
//     if (onStopReading) {
//       onStopReading();
//     }
//   };

//   const handleContainerClick = (e: React.MouseEvent) => {
//     if (!isReading) {
//       const target = e.target as HTMLElement;
//       const textLayer = target.closest('.react-pdf__Page__textLayer');
//       if (textLayer) {
//         const selection = window.getSelection();
//         if (selection && selection.toString()) {
//           onTextSelection(selection.toString(), pageNumber - 1);
//         }
//       }
//     }
//   };

//   if (!document) {
//     return (
//       <Paper elevation={3} className="p-6 text-center">
//         <Typography variant="h6">No document loaded</Typography>
//         <Typography variant="body2" className="mt-2 text-gray-600">
//           Please upload a PDF document to get started
//         </Typography>
//       </Paper>
//     );
//   }

//   if (error) {
//     return (
//       <Paper elevation={3} className="p-6 text-center">
//         <Typography variant="h6" color="error">Error</Typography>
//         <Typography variant="body2" className="mt-2">
//           {error}
//         </Typography>
//       </Paper>
//     );
//   }

//   const pages = Array.from(new Array(numPages || 0), (_, index) => {
//     const pageNum = index + 1;
//     const shouldRender = renderedPages.has(pageNum);
//     const pageHighlights = highlights.filter(h => h.pageIndex === pageNum - 1);

//     return (
//       <div key={index} className="page">
//         {shouldRender && (
//           <Box position="relative">
//             <Page
//               key={`page_${pageNum}_${scale}`}
//               pageNumber={pageNum}
//               scale={scale}
//               renderTextLayer={true}
//               renderAnnotationLayer={false}
//               className="page-content"
//             />
//             {pageHighlights.map((highlight, idx) => (
//               highlight.coordinates && (
//                 <Box
//                   key={`highlight_${idx}`}
//                   sx={{
//                     position: 'absolute',
//                     left: `${highlight.coordinates.x}px`,
//                     top: `${highlight.coordinates.y}px`,
//                     width: `${highlight.coordinates.width}px`,
//                     height: `${highlight.coordinates.height}px`,
//                     backgroundColor: currentWord === highlight.text.split(' ')[0] ? 'rgba(255, 255, 0, 0.5)' : 'rgba(255, 152, 0, 0.3)',
//                     pointerEvents: 'none',
//                   }}
//                 />
//               )
//             ))}
//           </Box>
//         )}
//       </div>
//     );
//   });

//   return (
//     <Paper
//       elevation={3}
//       //className="p-4 overflow-hidden"
//       //style={{ height: isMobile ? 'auto' : '100vh' }}
//     >
//       <Box className="flex justify-between items-center mb-4">
//         {/* <Typography variant="h6" noWrap className="max-w-xs overflow-hidden">
//           {document.name}
//         </Typography> */}
//         <Box className="flex items-center">
//           <IconButton
//             onClick={goToPrevPage}
//             disabled={pageNumber <= 1 || isReading}
//             aria-label="Previous page"
//           >
//             <ChevronLeft />
//           </IconButton>
//           <Typography variant="body2" className="mx-2">
//             Page {pageNumber} of {numPages || 0}
//           </Typography>
//           <IconButton
//             onClick={goToNextPage}
//             disabled={numPages === null || pageNumber >= numPages || isReading}
//             aria-label="Next page"
//           >
//             <ChevronRight />
//           </IconButton>
//           <Divider orientation="vertical" flexItem sx={{ mx: 1, height: '24px' }} />
//           {/* {isReading ? (
//             <Tooltip title="Stop reading">
//               <IconButton
//                 onClick={handleStopReading}
//                 aria-label="Stop reading"
//                 color="error"
//               >
//                 <StopCircle />
//               </IconButton>
//             </Tooltip>
//           ) : (
//             <Tooltip title="Read aloud">
//               <IconButton
//                 onClick={handleStartReading}
//                 aria-label="Read aloud"
//                 color="primary"
//               >
//                 <Play />
//               </IconButton>
//             </Tooltip>
//           )} */}
//         </Box>
//       </Box>

//       <Box
//         ref={containerRef}
//         //className="overflow-hidden"
//         style={{
//           height: isMobile ? '60vh' : 'auto',
//           position: 'relative',
//           pointerEvents: isReading ? 'none' : 'auto', // Disable interactions during reading
//         }}
//         onMouseUp={handleTextSelection}
//         onClick={handleContainerClick}
//       >
//         <Document
//           file={document.url}
//           onLoadSuccess={onDocumentLoadSuccess}
//           onLoadError={onDocumentLoadError}
//         >
//           <HTMLFlipBook
//             ref={flipBookRef}
//             className="w-full h-full"
//             width={550}
//             height={733}
//             size="stretch"
//             minWidth={315}
//             maxWidth={1000}
//             minHeight={400}
//             maxHeight={1533}
//             drawShadow={true}
//             flippingTime={300}
//             startPage={pageNumber - 1}
//             onFlip={handlePageFlip}
//             showCover={true}
//             style={{ margin: '0 auto' }}
//             usePortrait={true}
//             autoSize={true}
//             maxShadowOpacity={0.3}
//             mobileScrollSupport={true}
//             startZIndex={0}
//             clickEventForward={false} // Prevent click events from propagating
//             useMouseEvents={false} // Disable mouse-based flipping
//             swipeDistance={0} // Disable swipe-based flipping
//             showPageCorners={true}
//             disableFlipByClick={true}
//           >
//             {pages}
//           </HTMLFlipBook>
//         </Document>
//       </Box>

//       <style>{`
//         .page {
//           padding: 20px;
//           background: white;
//           border-radius: 4px;
//           box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
//         }
//         .page-content {
//           width: 100%;
//           height: 100%;
//           display: flex;
//           justify-content: center;
//           align-items: center;
//         }
//         .page.--left {
//           border-top-left-radius: 10px;
//           border-bottom-left-radius: 10px;
//         }
//         .page.--right {
//           border-top-right-radius: 10px;
//           border-bottom-right-radius: 10px;
//         }
//         ::selection {
//           background: rgba(255, 152, 0, 0.3);
//           color: inherit;
//         }
//         .react-pdf__Page__textLayer {
//           pointer-events: ${isReading ? 'none' : 'auto'};
//         }
//         .react-pdf__Page__canvas {
//           pointer-events: none;
//         }
//       `}</style>
//     </Paper>
//   );
// };

// export default PDFViewer;

interface PDFViewerProps {
  document: PDFDocument | null;
  highlights: HighlightArea[];
  onPageChange: (pageNumber: number) => Promise<void>;
  onTextSelection: (text: string, pageIndex: number) => void;
  currentHighlight: TextItem | null;
  pageHeight: number;
  isReading?: boolean;
  is3DMode?: boolean;
  hideControls?: boolean; // New prop to hide navigation controls
}

const PDFViewer: React.FC<PDFViewerProps> = ({
  document,
  highlights,
  onPageChange,
  onTextSelection,
  currentHighlight,
  pageHeight,
  isReading = false,
  is3DMode = false,
  hideControls = false,
}) => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));
  const [numPages, setNumPages] = useState<number | null>(null);
  const [pageNumber, setPageNumber] = useState<number>(1);
  const [scale, setScale] = useState<number>(isMobile ? 0.8 : 1.2);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [renderedPages, setRenderedPages] = useState<Set<number>>(new Set([1]));
  const flipBookRef = useRef<any>(null);
  const containerRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (document) {
      setPageNumber(document.currentPage);
      setIsLoading(true);
      setError(null);
    }
  }, [document]);

  useEffect(() => {
    if (document && flipBookRef.current) {
      const pageFlipInstance = flipBookRef.current.pageFlip();
      const targetPage = document.currentPage - 1;
      const currentPage = pageFlipInstance.getCurrentPageIndex();
      if (currentPage !== targetPage) {
        const delta = targetPage - currentPage;
        if (delta > 0) {
          for (let i = 0; i < delta; i++) {
            pageFlipInstance.flipNext();
          }
        } else if (delta < 0) {
          for (let i = 0; i < -delta; i++) {
            pageFlipInstance.flipPrev();
          }
        }
      }
    }
  }, [document?.currentPage]);

  useEffect(() => {
    const handleResize = () => {
      if (containerRef.current) {
        const width = containerRef.current.offsetWidth;
        setScale(isMobile ? width / 800 : width / 1200);
      }
    };
    handleResize();
    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, [isMobile]);

  useEffect(() => {
    if (numPages && pageNumber) {
      const pagesToRender = new Set<number>();
      pagesToRender.add(pageNumber);
      if (pageNumber > 1) pagesToRender.add(pageNumber - 1);
      if (pageNumber < numPages) pagesToRender.add(pageNumber + 1);
      setRenderedPages(pagesToRender);
    }
  }, [pageNumber, numPages]);

  const onDocumentLoadSuccess = ({ numPages }: { numPages: number }) => {
    setNumPages(numPages);
    setIsLoading(false);
  };

  const onDocumentLoadError = (error: Error) => {
    console.error('Error loading PDF:', error);
    setError('Failed to load the PDF document.');
    setIsLoading(false);
  };

  const handlePageFlip = (e: any) => {
    const newPageNumber = e.data + 1;
    setPageNumber(newPageNumber);
    onPageChange(newPageNumber);
  };

  const goToPrevPage = () => {
    if (flipBookRef.current && pageNumber > 1) {
      flipBookRef.current.pageFlip().flipPrev();
    }
  };

  const goToNextPage = () => {
    if (flipBookRef.current && numPages !== null && pageNumber < numPages) {
      flipBookRef.current.pageFlip().flipNext();
    }
  };

  const handleTextSelection = () => {
    if (isReading) return;
    const selection = window.getSelection();
    if (selection && selection.toString()) {
      onTextSelection(selection.toString(), pageNumber - 1);
    }
  };

  const handleContainerClick = (e: React.MouseEvent) => {
    if (!isReading) {
      const target = e.target as HTMLElement;
      const textLayer = target.closest('.react-pdf__Page__textLayer');
      if (textLayer) {
        const selection = window.getSelection();
        if (selection && selection.toString()) {
          onTextSelection(selection.toString(), pageNumber - 1);
        }
      }
    }
  };

  if (!document) {
    return (
      <Paper elevation={3} className="p-6 text-center">
        <Typography variant="h6">No document loaded</Typography>
        <Typography variant="body2" className="mt-2 text-gray-600">
          Please upload a PDF document to get started
        </Typography>
      </Paper>
    );
  }

  if (error) {
    return (
      <Paper elevation={3} className="p-6 text-center">
        <Typography variant="h6" color="error">Error</Typography>
        <Typography variant="body2" className="mt-2">
          {error}
        </Typography>
      </Paper>
    );
  }

  const pages = Array.from(new Array(numPages || 0), (_, index) => {
    const pageNum = index + 1;
    const shouldRender = renderedPages.has(pageNum);
    const pageHighlights = highlights.filter(h => h.pageIndex === pageNum - 1);

    return (
      <div key={index} className="page">
        {shouldRender && (
          <Box position="relative">
            <Page
              key={`page_${pageNum}_${scale}`}
              pageNumber={pageNum}
              scale={scale}
              renderTextLayer={true}
              renderAnnotationLayer={false}
              className="page-content"
            />
            {pageHighlights.map((highlight, idx) => (
              highlight.coordinates && (
                <Box
                  key={`highlight_${idx}`}
                  sx={{
                    position: 'absolute',
                    left: `${highlight.coordinates.x * scale}px`,
                    top: `${(pageHeight - highlight.coordinates.y - highlight.coordinates.height) * scale}px`,
                    width: `${highlight.coordinates.width * scale}px`,
                    height: `${highlight.coordinates.height * scale}px`,
                    backgroundColor: 'rgba(255, 152, 0, 0.3)',
                    pointerEvents: 'none',
                  }}
                />
              )
            ))}
            {currentHighlight && currentHighlight.pageIndex === pageNum - 1 && (
              <Box
                sx={{
                  position: 'absolute',
                  left: 0,
                  top: `${(pageHeight - currentHighlight.coordinates.y - currentHighlight.coordinates.height) * scale}px`,
                  width: '100%',
                  height: `${currentHighlight.coordinates.height * scale}px`,
                  backgroundColor: 'rgba(255, 235, 59, 0.2)',
                  borderLeft: '4px solid #FF5722',
                  pointerEvents: 'none',
                  zIndex: 100
                }}
              />
            )}
          </Box>
        )}
      </div>
    );
  });

  return (
    <Paper elevation={3} sx={{ height: '100vh', display: 'flex', flexDirection: 'column' }}>
      {!hideControls && (
        <Box className="flex justify-between items-center mb-4" sx={{ p: 1 }}>
          <Box className="flex items-center">
            <IconButton
              onClick={goToPrevPage}
              disabled={pageNumber <= 1 || isReading}
              aria-label="Previous page"
            >
              <ChevronLeft />
            </IconButton>
            <Typography variant="body2" className="mx-2">
              Page {pageNumber} of {numPages || 0}
            </Typography>
            <IconButton
              onClick={goToNextPage}
              disabled={numPages === null || pageNumber >= numPages || isReading}
              aria-label="Next page"
            >
              <ChevronRight />
            </IconButton>
          </Box>
        </Box>
      )}

      <Box
        ref={containerRef}
        style={{
          height: isMobile ? '60vh' : 'auto',
          position: 'relative',
          pointerEvents: isReading ? 'none' : 'auto',
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center',
        }}
        onMouseUp={handleTextSelection}
        onClick={handleContainerClick}
      >
        <Document
          file={document.url}
          onLoadSuccess={onDocumentLoadSuccess}
          onLoadError={onDocumentLoadError}
        >
          <HTMLFlipBook
            ref={flipBookRef}
            className="w-full h-full"
            width={550}
            height={733}
            size="stretch"
            minWidth={315}
            maxWidth={1000}
            minHeight={400}
            maxHeight={1533}
            drawShadow={true}
            flippingTime={300}
            startPage={pageNumber - 1}
            onFlip={handlePageFlip}
            showCover={true}
            style={{ margin: '0 auto' }}
            usePortrait={true}
            autoSize={true}
            maxShadowOpacity={0.3}
            mobileScrollSupport={true}
            startZIndex={0}
            clickEventForward={false}
            useMouseEvents={false}
            swipeDistance={0}
            showPageCorners={true}
            disableFlipByClick={true}
          >
            {pages}
          </HTMLFlipBook>
        </Document>
      </Box>

      <style>{`
        .page { padding: 20px; background: white; border-radius: 4px; box-shadow: 0 0 20px rgba(0, 0, 0, 0.1); }
        .page-content { width: 100%; height: 100%; display: flex; justify-content: center; align-items: center; }
        .page.--left { border-top-left-radius: 10px; border-bottom-left-radius: 10px; }
        .page.--right { border-top-right-radius: 10px; border-bottom-right-radius: 10px; }
        ::selection { background: rgba(255, 152, 0, 0.3); color: inherit; }
        .react-pdf__Page__textLayer { pointer-events: ${isReading ? 'none' : 'auto'}; }
        .react-pdf__Page__canvas { pointer-events: none; }
      `}</style>
    </Paper>
  );
};

export default PDFViewer;