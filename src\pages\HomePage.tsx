// import React, { useState, useEffect, useCallback } from 'react';
// import { Container, Box, Paper, Typography, Divider } from '@mui/material';
// import PDFViewer from '../components/PDFViewer';
// import AudioControls from '../components/AudioControls';
// import Header from '../components/Header';
// import Footer from '../components/Footer';
// import { usePDFDocument } from '../hooks/usePDFDocument';
// import { useSpeechSynthesis } from '../hooks/useSpeechSynthesis';

// const HomePage: React.FC = () => {
//   const {
//     documentInfo,
//     currentPageText,
//     highlights,
//     loadDocument,
//     setCurrentPage,
//     addHighlight,
//     clearHighlights,
//     isLoading,
//   } = usePDFDocument();

//   const {
//     voices,
//     options,
//     speaking,
//     paused,
//     speak,
//     pause,
//     resume,
//     stop,
//     setOptions,
//   } = useSpeechSynthesis();

//   const [currentHighlightedText, setCurrentHighlightedText] = useState('');
//   const [currentWord, setCurrentWord] = useState('');
//   const [isAutoReading, setIsAutoReading] = useState(false);

//   useEffect(() => {
//     loadDocument('/src/assets/sample.pdf');
//   }, [loadDocument]);

//   const handleTextSelection = (text: string, pageIndex: number) => {
//     if (text && documentInfo) {
//       if (speaking) {
//         stop();
//       }
//       setCurrentHighlightedText(text);
//       addHighlight({
//         pageIndex,
//         text,
//       });
//       speak(
//         text,
//         [],
//         pageIndex,
//         handleWordBoundary,
//         () => {}
//       );
//     }
//   };

//   const handleWordBoundary = useCallback(
//     (event: SpeechSynthesisEvent) => {
//       if (event.charIndex !== undefined && event.charLength !== undefined) {
//         const word = currentHighlightedText.substring(
//           event.charIndex,
//           event.charIndex + event.charLength
//         );
//         setCurrentWord(word);
//       }
//     },
//     [currentHighlightedText]
//   );

//   const handleReadingComplete = useCallback(async () => {
//     if (!documentInfo || !isAutoReading) return;

//     const currentPage = documentInfo.currentPage;
//     let nextPage;

//     if (currentPage === 1) {
//       nextPage = 2;
//     } else {
//       nextPage = currentPage % 2 === 0 ? currentPage + 1 : currentPage + 2;
//     }

//     if (nextPage <= documentInfo.totalPages) {
//       stop();
//       await setCurrentPage(nextPage);
//       setTimeout(() => {
//         if (isAutoReading) {
//           setIsAutoReading(true);
//         }
//       }, 500);
//     } else {
//       setIsAutoReading(false);
//       stop();
//       clearHighlights();
//     }
//   }, [documentInfo, isAutoReading, setCurrentPage, stop, clearHighlights]);

//   const handlePlay = () => {
//     setIsAutoReading(true);
//   };

//   const handleNext = useCallback(async () => {
//     if (!documentInfo) return;
//     stop();
//     setIsAutoReading(false);
//     clearHighlights();

//     const currentPage = documentInfo.currentPage;
//     let nextPage;

//     if (currentPage === 1) {
//       nextPage = 2;
//     } else {
//       nextPage = currentPage % 2 === 0 ? currentPage + 1 : currentPage + 2;
//     }

//     if (nextPage <= documentInfo.totalPages) {
//       await setCurrentPage(nextPage);
//     }
//   }, [documentInfo, setCurrentPage, stop, clearHighlights]);

//   const handlePrevious = useCallback(async () => {
//     if (!documentInfo) return;
//     stop();
//     setIsAutoReading(false);
//     clearHighlights();

//     const currentPage = documentInfo.currentPage;
//     let prevPage;

//     if (currentPage === 2) {
//       prevPage = 1;
//     } else if (currentPage === 3) {
//       prevPage = 2;
//     } else if (currentPage > 3) {
//       prevPage = currentPage % 2 === 0 ? currentPage - 1 : currentPage - 2;
//       prevPage = Math.max(2, prevPage);
//     } else {
//       prevPage = 1;
//     }

//     if (prevPage >= 1) {
//       await setCurrentPage(prevPage);
//     }
//   }, [documentInfo, setCurrentPage, stop, clearHighlights]);

//   useEffect(() => {
//     if (
//       !isAutoReading ||
//       !documentInfo ||
//       speaking ||
//       isLoading ||
//       !documentInfo.pdfDoc
//     )
//       return;

//     const readingTimeout = setTimeout(async () => {
//       let textToRead = currentPageText;
//       let pageIndex = documentInfo.currentPage - 1;
//       let isCurrentPageEmpty = !textToRead.trim();

//       if (documentInfo.currentPage === 1) {
//         if (textToRead) {
//           textToRead = textToRead.replace(/\s+/g, ' ').trim();
//         }

//         const coverPageImageDescriptions =
//           documentInfo.imageDescriptions?.filter(
//             (img) => img.pageIndex === pageIndex
//           ) || [];

//         const coverPageImageText =
//           coverPageImageDescriptions.length > 0
//             ? coverPageImageDescriptions.map((img) => img.description).join('. ')
//             : '';

//         let coverPageText = '';

//         if (!isCurrentPageEmpty) {
//           coverPageText = textToRead;
//           if (coverPageImageText) {
//             coverPageText += `. ${coverPageImageText}`;
//           }
//         } else if (coverPageImageText) {
//           coverPageText = `Cover page contains images: ${coverPageImageText}`;
//         }

//         if (coverPageText === '') {
//           handleReadingComplete();
//           return;
//         }

//         setCurrentHighlightedText(textToRead);

//         speak(
//           coverPageText,
//           [],
//           pageIndex,
//           handleWordBoundary,
//           handleReadingComplete
//         );
//         return;
//       }

//       let nextPageText = '';
//       let nextPageIndex = pageIndex + 1;
//       let isNextPageEmpty = true;

//       if (nextPageIndex < documentInfo.totalPages) {
//         try {
//           const page = await documentInfo.pdfDoc!.getPage(
//             documentInfo.currentPage + 1
//           );
//           const textContent = await page.getTextContent();
//           nextPageText = textContent.items.map((item: any) => item.str).join(' ');

//           isNextPageEmpty = !nextPageText.trim();

//           if (nextPageText) {
//             nextPageText = nextPageText.replace(/\s+/g, ' ').trim();
//           }
//         } catch (err) {
//           console.error('Failed to extract next page text:', err);
//         }
//       }

//       const currentPageImageDescriptions =
//         documentInfo.imageDescriptions?.filter(
//           (img) => img.pageIndex === pageIndex
//         ) || [];

//       const nextPageImageDescriptions =
//         documentInfo.imageDescriptions?.filter(
//           (img) => img.pageIndex === nextPageIndex
//         ) || [];

//       if (textToRead) {
//         textToRead = textToRead.replace(/\s+/g, ' ').trim();
//       }

//       const currentPageImageText =
//         currentPageImageDescriptions.length > 0
//           ? currentPageImageDescriptions.map((img) => img.description).join('. ')
//           : '';

//       const nextPageImageText =
//         nextPageImageDescriptions.length > 0
//           ? nextPageImageDescriptions.map((img) => img.description).join('. ')
//           : '';

//       let combinedText = '';

//       if (!isCurrentPageEmpty) {
//         combinedText += `Left page ${documentInfo.currentPage}: ${textToRead}`;
//         if (currentPageImageText) {
//           combinedText += `. ${currentPageImageText}`;
//         }
//       } else if (currentPageImageText) {
//         combinedText += `Left page ${documentInfo.currentPage} contains images: ${currentPageImageText}`;
//       }

//       if (nextPageIndex < documentInfo.totalPages) {
//         if (combinedText) {
//           combinedText += '. ';
//         }

//         if (!isNextPageEmpty) {
//           combinedText += `Right page ${documentInfo.currentPage + 1}: ${nextPageText}`;
//           if (nextPageImageText) {
//             combinedText += `. ${nextPageImageText}`;
//           }
//         } else if (nextPageImageText) {
//           combinedText += `Right page ${documentInfo.currentPage + 1} contains images: ${nextPageImageText}`;
//         }
//       }

//       if (combinedText === '') {
//         handleReadingComplete();
//         return;
//       }

//       setCurrentHighlightedText(textToRead);

//       speak(
//         combinedText,
//         [],
//         pageIndex,
//         handleWordBoundary,
//         handleReadingComplete
//       );
//     }, 300);

//     return () => clearTimeout(readingTimeout);
//   }, [
//     isAutoReading,
//     documentInfo,
//     currentPageText,
//     speak,
//     handleWordBoundary,
//     handleReadingComplete,
//     isLoading,
//     speaking,
//   ]);

//   return (
//     <Box className="min-h-screen flex flex-col bg-gray-50">
//       <Header onHistoryClick={() => {}} />
//       <Container maxWidth="lg" sx={{ flexGrow: 1, py: 3, display: 'flex', flexDirection: 'column' }}>
//         <Paper elevation={3} sx={{ p: 3, mb: 3 }}>
//           <Typography variant="h4" component="h1" gutterBottom align="center" fontWeight="bold" color="primary">
//             TSLS Handbook PDF to Audio
//           </Typography>
//           <Divider sx={{ mb: 2 }} />
//           {/* <Typography variant="body1" align="center" color="textSecondary" sx={{ mb: 2 }}>
//             Experience seamless reading of your handbook with side-by-side page view and automatic audio narration.
//           </Typography> */}
//           <AudioControls
//             playing={speaking}
//             paused={paused}
//             onPlay={handlePlay}
//             onPause={pause}
//             onResume={resume}
//             onStop={() => {
//               stop();
//               setIsAutoReading(false);
//               clearHighlights();
//             }}
//             onNext={handleNext}
//             onPrevious={handlePrevious}
//             voiceOptions={options}
//             setVoiceOptions={setOptions}
//             availableVoices={voices}
//           />
//         </Paper>
//         <Paper elevation={3} sx={{ flexGrow: 1, p: 2, display: 'flex', flexDirection: 'column' }}>
//           <PDFViewer
//             document={documentInfo}
//             highlights={highlights}
//             onPageChange={setCurrentPage}
//             onTextSelection={handleTextSelection}
//             onReadingComplete={handleReadingComplete}
//             currentWord={currentWord}
//             onStartReading={handlePlay}
//             onStopReading={() => {
//               stop();
//               setIsAutoReading(false);
//               clearHighlights();
//             }}
//             isReading={isAutoReading || speaking}
//           />
//         </Paper>
//       </Container>
//       <Footer />
//     </Box>
//   );
// };

// export default HomePage;


import React, { useState, useEffect, useCallback, useRef } from 'react';
import { Container, Box, Paper, Typography, Divider } from '@mui/material';
import PDFViewer from '../components/PDFViewer';
import AudioControls from '../components/AudioControls';
import Header from '../components/Header';
import Footer from '../components/Footer';
import { usePDFDocument } from '../hooks/usePDFDocument';
import { useSpeechSynthesis } from '../hooks/useSpeechSynthesis';
import type { TextItem } from '../types';
import { extractTextFromPage } from '../utils/pdfHelpers';

const HomePage: React.FC = () => {
  const {
    documentInfo,
    currentPageText,
    highlights,
    loadDocument,
    setCurrentPage,
    addHighlight,
    clearHighlights,
    isLoading,
  } = usePDFDocument();

  const {
    voices,
    options,
    speaking,
    paused,
    speak,
    pause,
    resume,
    stop,
    setOptions,
  } = useSpeechSynthesis();

  const [currentHighlightedText, setCurrentHighlightedText] = useState('');
  const [currentHighlight, setCurrentHighlight] = useState<TextItem | null>(null);
  const [spreadTextItems, setSpreadTextItems] = useState<TextItem[]>([]);
  const [isAutoReading, setIsAutoReading] = useState(false);
  const containerRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    loadDocument('/src/assets/sample.pdf');
  }, [loadDocument]);

  const handleTextSelection = (text: string, pageIndex: number) => {
    if (text && documentInfo) {
      if (speaking) {
        stop();
      }
      setCurrentHighlightedText(text);
      addHighlight({ pageIndex, text });
      speak(
        text,
        [],
        pageIndex,
        (event) => {
          // Optional: Handle boundary for selected text if needed
        },
        () => {}
      );
    }
  };

  const getSpreadTextAndItems = useCallback(async (pageNumber: number) => {
    if (!documentInfo || !documentInfo.pdfDoc) return { text: '', textItems: [] };

    let text = '';
    let textItems: TextItem[] = [];

    if (pageNumber === 1) {
      const { fullText, textItems: pageTextItems } = await extractTextFromPage(documentInfo.pdfDoc, 1);
      text = fullText;
      textItems = pageTextItems;
    } else {
      const { fullText: leftText, textItems: leftTextItems } = await extractTextFromPage(documentInfo.pdfDoc, pageNumber);
      const rightPage = pageNumber + 1 <= documentInfo.totalPages ? pageNumber + 1 : null;
      let rightText = '';
      let rightTextItems: TextItem[] = [];
      if (rightPage) {
        const { fullText, textItems } = await extractTextFromPage(documentInfo.pdfDoc, rightPage);
        rightText = fullText;
        rightTextItems = textItems.map(item => ({
          ...item,
          startIndex: item.startIndex + leftText.length + 1,
          endIndex: item.endIndex + leftText.length + 1,
        }));
      }
      text = `${leftText} ${rightText}`.trim();
      textItems = [...leftTextItems, ...rightTextItems];
    }

    return { text, textItems };
  }, [documentInfo]);

const handleReadingComplete = useCallback(async () => {
    if (!documentInfo || !isAutoReading) return;

    const currentPage = documentInfo.currentPage;
    let nextPage;

    if (currentPage % 2 === 1) {
      nextPage = currentPage + 1;
    } else {
      nextPage = currentPage + 1;
    }

    if (nextPage > documentInfo.totalPages) {
      setIsAutoReading(false);
      stop();
      clearHighlights();
      setCurrentHighlight(null);
    } else {
      stop();
      await setCurrentPage(nextPage);
      setTimeout(() => {
        if (isAutoReading) {
          setIsAutoReading(true);
        }
      }, 500);
    }
  }, [documentInfo, isAutoReading, setCurrentPage, stop, clearHighlights]);

  useEffect(() => {
    if (!isAutoReading || !documentInfo || speaking || isLoading || !documentInfo.pdfDoc) return;

    const readingTimeout = setTimeout(async () => {
      const { text, textItems } = await getSpreadTextAndItems(documentInfo.currentPage);
      setSpreadTextItems(textItems);
      setCurrentHighlightedText(text);
      speak(
        text,
        [],
        documentInfo.currentPage - 1,
        (event) => {
          if (event.charIndex !== undefined && event.charLength !== undefined) {
            const charIndex = event.charIndex;
            const textItem = textItems.find(
              item => charIndex >= item.startIndex && charIndex < item.endIndex
            );
            if (textItem) {
              // Create a new TextItem with the exact word being spoken
              const word = text.substring(charIndex, charIndex + event.charLength);
              const wordStart = Math.max(charIndex, textItem.startIndex);
              const wordEnd = Math.min(charIndex + event.charLength, textItem.endIndex);
              const wordWidth = textItem.coordinates.width * ((wordEnd - wordStart) / (textItem.endIndex - textItem.startIndex));
              
              const highlightItem = {
                ...textItem,
                text: word,
                startIndex: wordStart,
                endIndex: wordEnd,
                coordinates: {
                  ...textItem.coordinates,
                  x: 0, // Start from the left edge
                  width: containerRef.current?.offsetWidth || window.innerWidth, // Extend to full width
                  height: textItem.coordinates.height // Use full height for line highlighting
                }
              };
              
              setCurrentHighlight(highlightItem);
            }
          }
        },
        () => {
          setCurrentHighlight(null);
          handleReadingComplete();
        }
      );
    }, 300);

    return () => clearTimeout(readingTimeout);
  }, [isAutoReading, documentInfo, speaking, isLoading, getSpreadTextAndItems, speak, handleReadingComplete]);

  return (
    <Box className="min-h-screen flex flex-col bg-gray-100" sx={{ height: '100vh', overflow: 'hidden' }}>
      <Header onHistoryClick={() => {}} />
      <Container maxWidth="xl" sx={{ flexGrow: 1, py: 1, display: 'flex', flexDirection: 'column', height: 'calc(100vh - 140px)', overflow: 'hidden' }}>
        {/* Audio Controls positioned at the top */}
        <Paper elevation={3} sx={{ p: 1.5, mb: 1 }}>
          <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', flexWrap: 'wrap', gap: 2 }}>
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
              <AudioControls
                playing={speaking}
                paused={paused}
                onPlay={() => setIsAutoReading(true)}
                onPause={pause}
                onResume={resume}
                onStop={() => {
                  stop();
                  setIsAutoReading(false);
                  clearHighlights();
                  setCurrentHighlight(null);
                }}
                onNext={async () => {
                  if (!documentInfo) return;
                  stop();
                  setIsAutoReading(false);
                  clearHighlights();
                  setCurrentHighlight(null);
                  const nextPage = documentInfo.currentPage + 1;
                  if (nextPage <= documentInfo.totalPages) {
                    await setCurrentPage(nextPage);
                  }
                }}
                onPrevious={async () => {
                  if (!documentInfo) return;
                  stop();
                  setIsAutoReading(false);
                  clearHighlights();
                  setCurrentHighlight(null);
                  const prevPage = documentInfo.currentPage - 1;
                  if (prevPage >= 1) {
                    await setCurrentPage(prevPage);
                  }
                }}
                voiceOptions={options}
                setVoiceOptions={setOptions}
                availableVoices={voices}
              />
            </Box>

            {/* Page information */}
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
              <Typography variant="body2" color="text.secondary">
                {documentInfo ? `Page ${documentInfo.currentPage} of ${documentInfo.totalPages}` : 'No document loaded'}
              </Typography>
              {isAutoReading && (
                <Typography variant="body2" color="primary">
                  Auto-reading in progress...
                </Typography>
              )}
            </Box>
          </Box>
        </Paper>

        {/* PDF Viewer taking full width */}
        <Box sx={{ flexGrow: 1, overflow: 'hidden' }} ref={containerRef}>
          <Paper
            elevation={3}
            sx={{
              height: '100%',
              p: 1,
              display: 'flex',
              flexDirection: 'column',
              overflow: 'hidden'
            }}
          >
            <PDFViewer
              document={documentInfo}
              highlights={highlights}
              onPageChange={setCurrentPage}
              onTextSelection={handleTextSelection}
              currentHighlight={currentHighlight}
              pageHeight={documentInfo?.pageHeight || 0}
              isReading={isAutoReading || speaking}
              hideControls={true}
            />
          </Paper>
        </Box>
      </Container>
      <Footer />
    </Box>
  );
};

export default HomePage;